import { z } from 'zod';  

// Client Types
export enum ClientType {
  WEB_APP = 'WEB_APP',
  MOBILE_APP = 'MOBILE_APP',
  SDK_WIDGET = 'SDK_WIDGET',
  API_CLIENT = 'API_CLIENT',
  INTERNAL_SERVICE = 'INTERNAL_SERVICE',
}

export enum ConnectionStatus {
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
  RECONNECTING = 'RECONNECTING',
  SUSPENDED = 'SUSPENDED',
}

export enum ChannelType {
  AGENT_EVENTS = 'AGENT_EVENTS',
  TOOL_EVENTS = 'TOOL_EVENTS',
  WORKFLOW_EVENTS = 'WORKFLOW_EVENTS',
  PROVIDER_EVENTS = 'PROVIDER_EVENTS',
  SYSTEM_EVENTS = 'SYSTEM_EVENTS',
  PRIVATE_USER = 'PRIVATE_USER',
  ORGANIZATION = 'ORGANIZATION',
}

export enum EventStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
}

// Zod Schemas
export const ApiXConnectionSchema = z.object({
  sessionId: z.string(),
  clientType: z.nativeEnum(ClientType),
  authentication: z.object({
    token: z.string(),
    organizationId: z.string().optional(),
  }),
  subscriptions: z.array(z.string()),
  metadata: z.record(z.any()).optional(),
});

export const ApiXEventSchema = z.object({
  type: z.string(),
  channel: z.string(),
  payload: z.any(),
  metadata: z.object({
    timestamp: z.number(),
    version: z.string(),
    correlation_id: z.string().optional(),
  }).optional(),
});

export const ApiXSubscriptionSchema = z.object({
  channels: z.array(z.string()),
  filters: z.record(z.any()).optional(),
  acknowledgment: z.boolean().default(false),
});

export const ApiXHeartbeatSchema = z.object({
  timestamp: z.number(),
  connectionId: z.string(),
});

export const ApiXErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  details: z.any().optional(),
  timestamp: z.number(),
});

// Types
export type ApiXConnectionData = z.infer<typeof ApiXConnectionSchema>;
export type ApiXEventData = z.infer<typeof ApiXEventSchema>;
export type ApiXSubscriptionData = z.infer<typeof ApiXSubscriptionSchema>;
export type ApiXHeartbeatData = z.infer<typeof ApiXHeartbeatSchema>;
export type ApiXErrorData = z.infer<typeof ApiXErrorSchema>;

// Event Types
export const APIX_EVENTS = {
  CONNECTION_ESTABLISHED: 'connection.established',
  CONNECTION_ERROR: 'connection.error',
  CONNECTION_DISCONNECTED: 'connection.disconnected',
  SUBSCRIPTION_ADDED: 'subscription.added',
  SUBSCRIPTION_REMOVED: 'subscription.removed',
  HEARTBEAT_PING: 'heartbeat.ping',
  HEARTBEAT_PONG: 'heartbeat.pong',
  MESSAGE_QUEUED: 'message.queued',
  MESSAGE_SENT: 'message.sent',
  MESSAGE_FAILED: 'message.failed',
  RECONNECTION_ATTEMPT: 'reconnection.attempt',
  TOOL_CALL_START: 'tool_call_start',
  TOOL_CALL_RESULT: 'tool_call_result',
  TOOL_CALL_ERROR: 'tool_call_error',
  AGENT_STATUS_UPDATE: 'agent_status_update',
  WORKFLOW_STATE_CHANGE: 'workflow_state_change',
  PROVIDER_HEALTH_UPDATE: 'provider_health_update',
  SYSTEM_NOTIFICATION: 'system_notification',
} as const;

// Channel Names
export const APIX_CHANNELS = {
  SYSTEM: 'system',
  AGENTS: 'agents',
  TOOLS: 'tools',
  WORKFLOWS: 'workflows',
  PROVIDERS: 'providers',
  NOTIFICATIONS: 'notifications',
  ANALYTICS: 'analytics',
  BILLING: 'billing',
  HITL: 'hitl',
  KNOWLEDGE: 'knowledge',
  WIDGETS: 'widgets',
} as const;

// Connection Interface
export interface ApiXConnection {
  id: string;
  sessionId: string;
  organizationId: string;
  userId?: string;
  clientType: ClientType;
  channels: string[];
  metadata?: Record<string, any>;
  connectedAt: Date;
  lastHeartbeat: Date;
  status: ConnectionStatus;
  ipAddress?: string;
  userAgent?: string;
}

// Event Interface
export interface ApiXEvent {
  id: string;
  eventType: string;
  channel: string;
  payload: any;
  sessionId?: string;
  organizationId?: string;
  userId?: string;
  acknowledgment: boolean;
  retryCount: number;
  maxRetries: number;
  status: EventStatus;
  processedAt?: Date;
  failedAt?: Date;
  error?: string;
  createdAt: Date;
  metadata?: Record<string, any>;
  correlationId?: string;
}

// Channel Interface
export interface ApiXChannel {
  id: string;
  name: string;
  type: ChannelType;
  organizationId?: string;
  permissions: Record<string, any>;
  subscribers: number;
  isActive: boolean;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Subscription Interface
export interface ApiXSubscription {
  id: string;
  connectionId: string;
  channelName: string;
  filters?: Record<string, any>;
  permissions?: Record<string, any>;
  createdAt: Date;
}

// Metrics Interface
export interface ApiXMetrics {
  id: string;
  connectionId: string;
  eventType: string;
  channel: string;
  latency: number;
  throughput: number;
  errorCount: number;
  timestamp: Date;
  organizationId?: string;
}