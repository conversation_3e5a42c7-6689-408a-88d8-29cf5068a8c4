import { Injectable, Logger } from '@nestjs/common';
import { Server } from 'socket.io';
import { PrismaService } from '../database/prisma.service';
import { RedisService } from '../redis/redis.service';
import { MessageQueueManagerService } from './message-queue-manager.service';
import { SubscriptionManagerService } from './subscription-manager.service';
import { AuditLoggerService } from './audit-logger.service';
import { ApiXEvent, EventStatus, APIX_CHANNELS } from './apix.types';

@Injectable()
export class EventRouterService {
  private readonly logger = new Logger(EventRouterService.name);
  private server: Server;

  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
    private readonly messageQueueManager: MessageQueueManagerService,
    private readonly subscriptionManager: SubscriptionManagerService,
    private readonly auditLogger: AuditLoggerService,
  ) {}

  setServer(server: Server) {
    this.server = server;
  }

  async routeEvent(eventData: Partial<ApiXEvent>): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Create event record
      const event = await this.prisma.event.create({
        data: {
          eventType: eventData.eventType!,
          channel: eventData.channel!,
          payload: eventData.payload,
          sessionId: eventData.sessionId,
          organizationId: eventData.organizationId,
          userId: eventData.userId,
          acknowledgment: eventData.acknowledgment || false,
          retryCount: 0,
          maxRetries: eventData.maxRetries || 3,
          status: EventStatus.PENDING,
          metadata: eventData.metadata,
          correlationId: eventData.correlationId,
        },
      });

      // Route based on channel type
      await this.routeByChannel(event);

      // Update event status
      await this.prisma.event.update({
        where: { id: event.id },
        data: {
          status: EventStatus.PROCESSING,
          processedAt: new Date(),
        },
      });

      // Audit log
      await this.auditLogger.logEvent({
        eventId: event.id,
        eventType: event.eventType,
        channel: event.channel,
        organizationId: event.organizationId,
        userId: event.userId,
        action: 'ROUTED',
        latency: Date.now() - startTime,
      });

    } catch (error) {
      this.logger.error('Failed to route event:', error);
      
      // Update event status to failed
      if (eventData.id) {
        await this.prisma.event.update({
          where: { id: eventData.id },
          data: {
            status: EventStatus.FAILED,
            failedAt: new Date(),
            error: error.message,
          },
        });
      }
      
      throw error;
    }
  }

  private async routeByChannel(event: ApiXEvent): Promise<void> {
    const { channel, organizationId } = event;

    switch (channel) {
      case APIX_CHANNELS.SYSTEM:
        await this.routeSystemEvent(event);
        break;
      case APIX_CHANNELS.AGENTS:
        await this.routeAgentEvent(event);
        break;
      case APIX_CHANNELS.TOOLS:
        await this.routeToolEvent(event);
        break;
      case APIX_CHANNELS.WORKFLOWS:
        await this.routeWorkflowEvent(event);
        break;
      case APIX_CHANNELS.PROVIDERS:
        await this.routeProviderEvent(event);
        break;
      case APIX_CHANNELS.NOTIFICATIONS:
        await this.routeNotificationEvent(event);
        break;
      case APIX_CHANNELS.ANALYTICS:
        await this.routeAnalyticsEvent(event);
        break;
      case APIX_CHANNELS.BILLING:
        await this.routeBillingEvent(event);
        break;
      case APIX_CHANNELS.HITL:
        await this.routeHITLEvent(event);
        break;
      case APIX_CHANNELS.KNOWLEDGE:
        await this.routeKnowledgeEvent(event);
        break;
      case APIX_CHANNELS.WIDGETS:
        await this.routeWidgetEvent(event);
        break;
      default:
        // Handle custom channels
        await this.routeCustomEvent(event);
    }
  }

  private async routeSystemEvent(event: ApiXEvent): Promise<void> {
    // Broadcast to all organization connections
    if (event.organizationId) {
      await this.broadcastToOrganization(event.organizationId, event);
    } else {
      // System-wide broadcast
      await this.broadcastToAll(event);
    }
  }

  private async routeAgentEvent(event: ApiXEvent): Promise<void> {
    // Route to agent-specific subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.AGENTS,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for agent processing
    await this.messageQueueManager.enqueue('agents', {
      type: 'agent_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeToolEvent(event: ApiXEvent): Promise<void> {
    // Route to tool-specific subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.TOOLS,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for tool processing
    await this.messageQueueManager.enqueue('tools', {
      type: 'tool_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeWorkflowEvent(event: ApiXEvent): Promise<void> {
    // Route to workflow-specific subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.WORKFLOWS,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for workflow processing
    await this.messageQueueManager.enqueue('workflows', {
      type: 'workflow_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeProviderEvent(event: ApiXEvent): Promise<void> {
    // Route to provider-specific subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.PROVIDERS,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for provider processing
    await this.messageQueueManager.enqueue('providers', {
      type: 'provider_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeNotificationEvent(event: ApiXEvent): Promise<void> {
    // Route to notification subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.NOTIFICATIONS,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for notification delivery
    await this.messageQueueManager.enqueue('notifications', {
      type: 'notification_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeAnalyticsEvent(event: ApiXEvent): Promise<void> {
    // Queue for analytics processing (no real-time delivery needed)
    await this.messageQueueManager.enqueue('analytics', {
      type: 'analytics_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeBillingEvent(event: ApiXEvent): Promise<void> {
    // Route to billing subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.BILLING,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for billing processing
    await this.messageQueueManager.enqueue('billing', {
      type: 'billing_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeHITLEvent(event: ApiXEvent): Promise<void> {
    // Route to HITL subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.HITL,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for HITL processing
    await this.messageQueueManager.enqueue('hitl', {
      type: 'hitl_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeKnowledgeEvent(event: ApiXEvent): Promise<void> {
    // Route to knowledge subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.KNOWLEDGE,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for knowledge processing
    await this.messageQueueManager.enqueue('knowledge', {
      type: 'knowledge_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeWidgetEvent(event: ApiXEvent): Promise<void> {
    // Route to widget subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      APIX_CHANNELS.WIDGETS,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
    
    // Queue for widget processing
    await this.messageQueueManager.enqueue('widgets', {
      type: 'widget_event',
      payload: event,
      organizationId: event.organizationId,
      userId: event.userId,
    });
  }

  private async routeCustomEvent(event: ApiXEvent): Promise<void> {
    // Route to custom channel subscribers
    const subscribers = await this.subscriptionManager.getChannelSubscribers(
      event.channel,
      event.organizationId
    );

    await this.deliverToSubscribers(subscribers, event);
  }

  private async deliverToSubscribers(subscribers: any[], event: ApiXEvent): Promise<void> {
    const deliveryPromises = subscribers.map(async (subscriber) => {
      try {
        if (this.server) {
          const socket = this.server.sockets.sockets.get(subscriber.connectionId);
          if (socket) {
            socket.emit('event', {
              id: event.id,
              type: event.eventType,
              channel: event.channel,
              payload: event.payload,
              metadata: {
                ...event.metadata,
                timestamp: Date.now(),
                correlationId: event.correlationId,
              },
            });
          }
        }
      } catch (error) {
        this.logger.error(`Failed to deliver event to ${subscriber.connectionId}:`, error);
        
        // Queue for retry
        await this.messageQueueManager.enqueue('retries', {
          type: 'failed_delivery',
          payload: {
            eventId: event.id,
            connectionId: subscriber.connectionId,
            error: error.message,
          },
          organizationId: event.organizationId,
          userId: event.userId,
        });
      }
    });

    await Promise.allSettled(deliveryPromises);
  }

  private async broadcastToOrganization(organizationId: string, event: ApiXEvent): Promise<void> {
    if (this.server) {
      this.server.to(`org:${organizationId}`).emit('event', {
        id: event.id,
        type: event.eventType,
        channel: event.channel,
        payload: event.payload,
        metadata: {
          ...event.metadata,
          timestamp: Date.now(),
          correlationId: event.correlationId,
        },
      });
    }
  }

  private async broadcastToAll(event: ApiXEvent): Promise<void> {
    if (this.server) {
      this.server.emit('event', {
        id: event.id,
        type: event.eventType,
        channel: event.channel,
        payload: event.payload,
        metadata: {
          ...event.metadata,
          timestamp: Date.now(),
          correlationId: event.correlationId,
        },
      });
    }
  }

  async getEventStats(organizationId?: string): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsByChannel: Record<string, number>;
    eventsByStatus: Record<string, number>;
    averageProcessingTime: number;
  }> {
    const where = organizationId ? { organizationId } : {};
    
    const [
      totalEvents,
      eventsByType,
      eventsByChannel,
      eventsByStatus,
      processingTimes,
    ] = await Promise.all([
      this.prisma.event.count({ where }),
      this.prisma.event.groupBy({
        by: ['eventType'],
        where,
        _count: { eventType: true },
      }),
      this.prisma.event.groupBy({
        by: ['channel'],
        where,
        _count: { channel: true },
      }),
      this.prisma.event.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      this.prisma.event.findMany({
        where: {
          ...where,
          processedAt: { not: null },
        },
        select: {
          createdAt: true,
          processedAt: true,
        },
      }),
    ]);

    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((sum, event) => {
          return sum + (event.processedAt!.getTime() - event.createdAt.getTime());
        }, 0) / processingTimes.length
      : 0;

    return {
      totalEvents,
      eventsByType: eventsByType.reduce((acc, item) => {
        acc[item.eventType] = item._count.eventType;
        return acc;
      }, {} as Record<string, number>),
      eventsByChannel: eventsByChannel.reduce((acc, item) => {
        acc[item.channel] = item._count.channel;
        return acc;
      }, {} as Record<string, number>),
      eventsByStatus: eventsByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>),
      averageProcessingTime,
    };
  }
}