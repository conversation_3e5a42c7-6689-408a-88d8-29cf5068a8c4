import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { PrismaService } from '../database/prisma.service';
import { MessageQueueManagerService } from './message-queue-manager.service';

@Injectable()
export class RetryManagerService {
  private readonly logger = new Logger(RetryManagerService.name);
  private readonly MAX_RETRY_ATTEMPTS = 5;
  private readonly BASE_DELAY = 1000; // 1 second
  private readonly MAX_DELAY = 300000; // 5 minutes
  private readonly RETRY_QUEUE_KEY = 'apix:retry_queue';
  private readonly DEAD_LETTER_QUEUE_KEY = 'apix:dead_letter_queue';

  constructor(
    private readonly redis: RedisService,
    private readonly prisma: PrismaService,
    private readonly messageQueueManager: MessageQueueManagerService,
  ) {
    // Start retry processor
    this.startRetryProcessor();
  }

  async scheduleRetry(
    eventId: string,
    connectionId: string,
    error: string,
    attempt: number = 1
  ): Promise<void> {
    try {
      if (attempt > this.MAX_RETRY_ATTEMPTS) {
        await this.moveToDeadLetterQueue(eventId, connectionId, error, attempt);
        return;
      }

      const delay = this.calculateExponentialBackoff(attempt);
      const retryTime = Date.now() + delay;

      const retryData = {
        eventId,
        connectionId,
        error,
        attempt,
        retryTime,
        createdAt: Date.now(),
      };

      // Add to retry queue with score as retry time for sorted set
      await this.redis.zadd(this.RETRY_QUEUE_KEY, retryTime, JSON.stringify(retryData));

      // Update event retry count
      await this.prisma.event.update({
        where: { id: eventId },
        data: {
          retryCount: attempt,
          status: 'FAILED',
          error: `Retry ${attempt}/${this.MAX_RETRY_ATTEMPTS}: ${error}`,
        },
      });

      // Track retry metrics
      await this.redis.incr('metrics:retries:scheduled');
      await this.redis.hincr('metrics:retries:by_attempt', attempt.toString(), 1);

      this.logger.log(`Retry scheduled: ${eventId} (attempt ${attempt}/${this.MAX_RETRY_ATTEMPTS}) in ${delay}ms`);

    } catch (error) {
      this.logger.error(`Failed to schedule retry for event ${eventId}:`, error);
    }
  }

  private calculateExponentialBackoff(attempt: number): number {
    const delay = this.BASE_DELAY * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
    return Math.min(delay + jitter, this.MAX_DELAY);
  }

  private async startRetryProcessor(): Promise<void> {
    const processRetries = async () => {
      try {
        const now = Date.now();
        
        // Get retries that are ready to be processed
        const readyRetries = await this.redis.zrangebyscore(
          this.RETRY_QUEUE_KEY,
          0,
          now,
          'LIMIT',
          0,
          10
        );

        for (const retryDataStr of readyRetries) {
          try {
            const retryData = JSON.parse(retryDataStr);
            await this.processRetry(retryData);
            
            // Remove from retry queue
            await this.redis.zrem(this.RETRY_QUEUE_KEY, retryDataStr);
            
          } catch (error) {
            this.logger.error('Failed to process retry:', error);
          }
        }

      } catch (error) {
        this.logger.error('Retry processor error:', error);
      }

      // Schedule next processing cycle
      setTimeout(processRetries, 5000); // Check every 5 seconds
    };

    processRetries();
  }

  private async processRetry(retryData: {
    eventId: string;
    connectionId: string;
    error: string;
    attempt: number;
    retryTime: number;
    createdAt: number;
  }): Promise<void> {
    try {
      // Get the original event
      const event = await this.prisma.event.findUnique({
        where: { id: retryData.eventId },
      });

      if (!event) {
        this.logger.warn(`Event not found for retry: ${retryData.eventId}`);
        return;
      }

      // Check if connection is still active
      const connection = await this.prisma.connection.findUnique({
        where: { id: retryData.connectionId },
      });

      if (!connection || connection.status !== 'CONNECTED') {
        this.logger.warn(`Connection not active for retry: ${retryData.connectionId}`);
        
        // Schedule another retry if connection might come back
        if (retryData.attempt < this.MAX_RETRY_ATTEMPTS) {
          await this.scheduleRetry(
            retryData.eventId,
            retryData.connectionId,
            'Connection not active',
            retryData.attempt + 1
          );
        } else {
          await this.moveToDeadLetterQueue(
            retryData.eventId,
            retryData.connectionId,
            'Connection permanently unavailable',
            retryData.attempt
          );
        }
        return;
      }

      // Attempt to deliver the event
      const success = await this.attemptDelivery(event, retryData.connectionId);

      if (success) {
        // Update event status
        await this.prisma.event.update({
          where: { id: retryData.eventId },
          data: {
            status: 'DELIVERED',
            processedAt: new Date(),
            error: null,
          },
        });

        // Track success metrics
        await this.redis.incr('metrics:retries:successful');
        
        this.logger.log(`Retry successful: ${retryData.eventId} (attempt ${retryData.attempt})`);

      } else {
        // Schedule another retry
        await this.scheduleRetry(
          retryData.eventId,
          retryData.connectionId,
          'Delivery failed on retry',
          retryData.attempt + 1
        );
      }

    } catch (error) {
      this.logger.error(`Failed to process retry for event ${retryData.eventId}:`, error);
      
      // Schedule another retry
      await this.scheduleRetry(
        retryData.eventId,
        retryData.connectionId,
        error.message,
        retryData.attempt + 1
      );
    }
  }

  private async attemptDelivery(event: any, connectionId: string): Promise<boolean> {
    try {
      // This would integrate with the gateway to attempt delivery
      // For now, simulate delivery attempt
      
      // Queue the event for delivery
      await this.messageQueueManager.enqueue('events', {
        type: 'retry_delivery',
        payload: {
          eventId: event.id,
          connectionId,
          event: {
            id: event.id,
            type: event.eventType,
            channel: event.channel,
            payload: event.payload,
            metadata: {
              ...event.metadata,
              retryAttempt: true,
              timestamp: Date.now(),
            },
          },
        },
        organizationId: event.organizationId,
        userId: event.userId,
      });

      return true; // Assume success for now

    } catch (error) {
      this.logger.error(`Delivery attempt failed for event ${event.id}:`, error);
      return false;
    }
  }

  private async moveToDeadLetterQueue(
    eventId: string,
    connectionId: string,
    error: string,
    finalAttempt: number
  ): Promise<void> {
    try {
      const deadLetterData = {
        eventId,
        connectionId,
        error,
        finalAttempt,
        movedAt: Date.now(),
      };

      // Add to dead letter queue
      await this.redis.lpush(this.DEAD_LETTER_QUEUE_KEY, JSON.stringify(deadLetterData));

      // Update event status
      await this.prisma.event.update({
        where: { id: eventId },
        data: {
          status: 'FAILED',
          failedAt: new Date(),
          error: `Max retries exceeded (${finalAttempt}): ${error}`,
        },
      });

      // Track dead letter metrics
      await this.redis.incr('metrics:retries:dead_letter');

      this.logger.warn(`Event moved to dead letter queue: ${eventId} after ${finalAttempt} attempts`);

    } catch (error) {
      this.logger.error(`Failed to move event to dead letter queue: ${eventId}`, error);
    }
  }

  async getRetryStats(organizationId?: string): Promise<{
    pendingRetries: number;
    deadLetterQueue: number;
    totalRetries: number;
    successfulRetries: number;
    retriesByAttempt: Record<string, number>;
    averageRetryDelay: number;
  }> {
    try {
      const [
        pendingRetries,
        deadLetterQueue,
        totalRetries,
        successfulRetries,
        retriesByAttempt,
      ] = await Promise.all([
        this.redis.zcard(this.RETRY_QUEUE_KEY),
        this.redis.llen(this.DEAD_LETTER_QUEUE_KEY),
        this.redis.get('metrics:retries:scheduled') || '0',
        this.redis.get('metrics:retries:successful') || '0',
        this.redis.hgetall('metrics:retries:by_attempt'),
      ]);

      // Calculate average retry delay
      const retryDelays = await this.redis.zrange(this.RETRY_QUEUE_KEY, 0, -1, 'WITHSCORES');
      let averageRetryDelay = 0;
      
      if (retryDelays.length > 0) {
        const now = Date.now();
        const delays = [];
        
        for (let i = 1; i < retryDelays.length; i += 2) {
          const retryTime = parseInt(retryDelays[i]);
          if (retryTime > now) {
            delays.push(retryTime - now);
          }
        }
        
        if (delays.length > 0) {
          averageRetryDelay = delays.reduce((sum, delay) => sum + delay, 0) / delays.length;
        }
      }

      return {
        pendingRetries,
        deadLetterQueue,
        totalRetries: parseInt(totalRetries),
        successfulRetries: parseInt(successfulRetries),
        retriesByAttempt: Object.entries(retriesByAttempt).reduce((acc, [attempt, count]) => {
          acc[attempt] = parseInt(count);
          return acc;
        }, {} as Record<string, number>),
        averageRetryDelay,
      };

    } catch (error) {
      this.logger.error('Failed to get retry stats:', error);
      return {
        pendingRetries: 0,
        deadLetterQueue: 0,
        totalRetries: 0,
        successfulRetries: 0,
        retriesByAttempt: {},
        averageRetryDelay: 0,
      };
    }
  }

  async getDeadLetterItems(limit: number = 50): Promise<any[]> {
    try {
      const items = await this.redis.lrange(this.DEAD_LETTER_QUEUE_KEY, 0, limit - 1);
      return items.map(item => JSON.parse(item));
    } catch (error) { 
      this.logger.error('Failed to get dead letter items:', error);
      return [];
    }
  }

  async reprocessDeadLetterItem(index: number): Promise<boolean> {
    try {
      // Get the item from dead letter queue
      const itemStr = await this.redis.lindex(this.DEAD_LETTER_QUEUE_KEY, index);
      if (!itemStr) {
        return false;
      }

      const item = JSON.parse(itemStr);
      
      // Remove from dead letter queue
      await this.redis.lrem(this.DEAD_LETTER_QUEUE_KEY, 1, itemStr);
      
      // Schedule for retry with attempt 1
      await this.scheduleRetry(item.eventId, item.connectionId, 'Manual reprocess', 1);
      
      this.logger.log(`Dead letter item reprocessed: ${item.eventId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to reprocess dead letter item at index ${index}:`, error);
      return false;
    }
  }

  async clearDeadLetterQueue(): Promise<number> {
    try {
      const count = await this.redis.llen(this.DEAD_LETTER_QUEUE_KEY);
      await this.redis.del(this.DEAD_LETTER_QUEUE_KEY);
      
      this.logger.log(`Cleared ${count} items from dead letter queue`);
      return count;

    } catch (error) {
      this.logger.error('Failed to clear dead letter queue:', error);
      return 0;
    }
  }

  async cancelRetry(eventId: string): Promise<boolean> {
    try {
      // Find and remove the retry from the queue
      const retries = await this.redis.zrange(this.RETRY_QUEUE_KEY, 0, -1);
      
      for (const retryStr of retries) {
        const retry = JSON.parse(retryStr);
        if (retry.eventId === eventId) {
          await this.redis.zrem(this.RETRY_QUEUE_KEY, retryStr);
          
          // Update event status
          await this.prisma.event.update({
            where: { id: eventId },
            data: {
              status: 'CANCELLED',
              error: 'Retry cancelled manually',
            },
          });
          
          this.logger.log(`Retry cancelled for event: ${eventId}`);
          return true;
        }
      }

      return false;

    } catch (error) {
      this.logger.error(`Failed to cancel retry for event ${eventId}:`, error);
      return false;
    }
  }

  async getRetryHistory(eventId: string): Promise<{
    event: any;
    retryAttempts: any[];
    currentStatus: string;
  }> {
    try {
      const event = await this.prisma.event.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        return {
          event: null,
          retryAttempts: [],
          currentStatus: 'NOT_FOUND',
        };
      }

      // Get retry attempts from audit logs
      const retryAttempts = await this.prisma.auditLog.findMany({
        where: {
          entityType: 'Event',
          entityId: eventId,
          action: { contains: 'RETRY' },
        },
        orderBy: { timestamp: 'asc' },
      });

      return {
        event,
        retryAttempts,
        currentStatus: event.status,
      };

    } catch (error) {
      this.logger.error(`Failed to get retry history for event ${eventId}:`, error);
      return {
        event: null,
        retryAttempts: [],
        currentStatus: 'ERROR',
      };
    }
  }
}