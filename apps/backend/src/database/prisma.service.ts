import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient, Prisma, DefaultArgs } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  
  event: Prisma.EventDelegate<DefaultArgs>
  connection: Prisma.ConnectionDelegate<DefaultArgs>
  auditLog: Prisma.AuditLogDelegate<DefaultArgs>
  subscription: Prisma.SubscriptionDelegate<DefaultArgs>
  channel: Prisma.ChannelDelegate<DefaultArgs>
  
  
  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get('DATABASE_URL'),
        },
      },
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async enableShutdownHooks(app: any) {
    this.$on('beforeExit', async () => {
      await app.close(); 
    });
  }


  $on(event: string | symbol, listener: (...args: any[]) => void): this {
    return super.$on(event, listener);
  }

  $connect(): Promise<void> {
    return super.$connect();
  }

  $disconnect(): Promise<void> {
    return super.$disconnect();
  }
  
}