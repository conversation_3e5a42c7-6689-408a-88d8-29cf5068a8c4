import { Injectable } from '@nestjs/common';
import Redis from 'ioredis';  
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RedisService {
  private readonly redis: Redis;

  constructor(private readonly configService: ConfigService) { 
    this.redis = new Redis(configService.get('REDIS_HOST'), configService.get('REDIS_PORT'));
  }

  async zadd(RETRY_QUEUE_KEY: string, retryTime: number, arg2: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.zadd(RETRY_QUEUE_KEY, retryTime, arg2);
  }
  async hincr(arg0: string, arg1: string, arg2: number): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.hincrby(arg0, arg1, arg2);
  }
  async zrangebyscore(RETRY_QUEUE_KEY: string, arg1: number, now: number, arg3: string, arg4: number, arg5: number): Promise<string[]> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.zrangebyscore(RETRY_QUEUE_KEY, arg1, now, arg3, arg4, arg5);
  }
  async zrem(RETRY_QUEUE_KEY: string, retryDataStr: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.zrem(RETRY_QUEUE_KEY, retryDataStr);
  }
  async zcard(RETRY_QUEUE_KEY: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.zcard(RETRY_QUEUE_KEY);
  }
  async llen(DEAD_LETTER_QUEUE_KEY: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.llen(DEAD_LETTER_QUEUE_KEY);
  }
  async zrange(RETRY_QUEUE_KEY: string, arg1: number, arg2: number, arg3: string): Promise<string[]> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.zrange(RETRY_QUEUE_KEY, arg1, arg2, arg3);
  }
  async lrange(DEAD_LETTER_QUEUE_KEY: string, arg1: number, arg2: number): Promise<string[]> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.lrange(DEAD_LETTER_QUEUE_KEY, arg1, arg2);
  }
  async lindex(DEAD_LETTER_QUEUE_KEY: string, index: number): Promise<string | null> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.lindex(DEAD_LETTER_QUEUE_KEY, index);
  }
  async lrem(DEAD_LETTER_QUEUE_KEY: string, arg1: number, itemStr: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.lrem(DEAD_LETTER_QUEUE_KEY, arg1, itemStr);
  }
  async incr(arg0: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.incr(arg0);
  }

  async get(key: string): Promise<string | null> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.get(key);
  }

  async set(key: string, value: string): Promise<'OK'> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.set(key, value);
  }

  async setex(key: string, seconds: number, value: string): Promise<'OK'> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.setex(key, seconds, value);
  }

  async del(...keys: string[]): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.del(...keys);
  }

  async exists(key: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.exists(key);
  }

  async keys(pattern: string): Promise<string[]> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.keys(pattern);
  }

  async hget(key: string, field: string): Promise<string | null> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.hget(key, field);
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.hset(key, field, value);
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.hgetall(key);
  }

  async lpush(key: string, ...values: string[]): Promise<number> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.lpush(key, ...values);
  }

  async rpop(key: string): Promise<string | null> {
    const client = this.redis.duplicate();
    await client.connect();
    return client.rpop(key);
  }

  async publish(channel: string, message: string): Promise<number> {
    const publisher = this.redis.duplicate();
    await publisher.connect();
    return publisher.publish(channel, message);
  }

  async subscribe(channel: string): Promise<void> {
    const subscriber = this.redis.duplicate();  
    await subscriber.connect();
    return subscriber.subscribe(channel);
  }

  getClient(): Redis {
    return this.redis;
  }
}