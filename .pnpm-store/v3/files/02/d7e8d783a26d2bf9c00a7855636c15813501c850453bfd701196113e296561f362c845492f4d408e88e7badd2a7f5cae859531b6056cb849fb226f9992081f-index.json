{"name": "fast-uri", "version": "3.0.6", "files": {".gitattributes": {"checkedAt": 1754554780950, "integrity": "sha512-ogGhgciV/rfs+QKsLZeuXKQn8VBoaRzqC5KdUFJCzI4hxMcQ9dYIhOfzt4pGbZ8hSOYeQyOnYAI7dJpKednSYQ==", "mode": 420, "size": 80}, "test/.gitkeep": {"checkedAt": 1754554780957, "integrity": "sha512-z4PhNX7vuL3xVChQ1m2AB9Yg5AULVxXcg/SpIdNs6c5H0NE8XYXysP+DGNKHfuwvY7kxvUdBeoGlODJ6+SfaPg==", "mode": 420, "size": 0}, "LICENSE": {"checkedAt": 1754554780967, "integrity": "sha512-+AuFj0XL//tbZYA2ZWiaMYeu9kEnYG6GvwamIZWYFg8DOUp4s5GenksuX7BwE27jClbgz9WaQPj00FhuufF2wQ==", "mode": 420, "size": 1763}, "test/ajv.test.js": {"checkedAt": 1754554780986, "integrity": "sha512-AM2mHJ7YwLztDn6KHzxhbo1L5Y3NQLKKkbEeD8HdobakdJgSF7v26HRa2MD8WvLqjOAht/GYaM7Oqf2mKfiPkQ==", "mode": 420, "size": 737}, "benchmark.js": {"checkedAt": 1754554781010, "integrity": "sha512-96IRfIROubku6ilYSbCz3pLrVbSgldAGEvw2XsXYLyO9GSqVB3ueVt6Qpk5vsP+b5ckX2icZe6XpH6o2d+RQUg==", "mode": 420, "size": 2677}, "test/compatibility.test.js": {"checkedAt": 1754554781025, "integrity": "sha512-NrV0AsflstbxGr2c/bN3eHlkdhjoyj00wIe/GzjXxYnFwcKhj+ZXyxWQTxqIArhQNhS8FKfFglHiSdEaAy60Nw==", "mode": 420, "size": 4117}, "test/equal.test.js": {"checkedAt": 1754554781041, "integrity": "sha512-V2I1Vq8p76oCLcYd7EUPpWGeaxO5eYwQVNd2zYjWW4l9eXy9nLUptkd00hSQwPBAVfwHsv73EwdzE9P2m18vMg==", "mode": 420, "size": 3300}, "eslint.config.js": {"checkedAt": 1754554781058, "integrity": "sha512-Aq2D8RMyooHE3NDEV0f4MVX9CK8ljjgg2VzEwY+YjcYOHDY+KWBjvibwPNziouyVH1sC8Y9fbmL531iffwuSFA==", "mode": 420, "size": 135}, "index.js": {"checkedAt": 1754554781076, "integrity": "sha512-D4SOSyPfLzoiDX22SjnXf4/pW9pqL64H0zddJD8+pvlM7U7KI8CHym5n+hZRHBYP+9X4KEJbLbYbeyMCeGRMJw==", "mode": 420, "size": 9523}, "test/parse.test.js": {"checkedAt": 1754554781095, "integrity": "sha512-n6c6xK1rSEvk+RyJwG9nkXoDj5TfYUqLuCci9wDX0fG9Z7Rt/TC3dmN5A1/+e49lzWtxya9Uauj8EGBtfka6/Q==", "mode": 420, "size": 13870}, "test/resolve.test.js": {"checkedAt": 1754554781108, "integrity": "sha512-VwBsISzZFvMaWx6mNxFS34bB3L7sPYothfV3eu2xK0knZR7CGzZlx7OHLg5VWr5k94042ZYawJhmBe7fp8GFLw==", "mode": 420, "size": 3803}, "lib/schemes.js": {"checkedAt": 1754554781126, "integrity": "sha512-29CoRPmOPp2vu4wGHLiVAmhK+GfL819XFzpIjPRY2ft2phz6rZVrxhclukk07hRryqjtNzU4CQbHW0ugS1Nulw==", "mode": 420, "size": 4716}, "lib/scopedChars.js": {"checkedAt": 1754554781141, "integrity": "sha512-hNsZR7FQ5n+0zASIe3n0QLek2olbLmUU+RocoP2jKffJ23/aZG7iqgRdKWk8QHb856fYeurSKPKWoNxVUUGG4Q==", "mode": 420, "size": 245}, "test/serialize.test.js": {"checkedAt": 1754554781163, "integrity": "sha512-0hkQC5taI8nGBJ0W/OvJ8tiPQwbCy+R/HsVcOPGuGMj0DHq/01iN81i+AniqKbcQEMhXrcfxDJN+pUuVZl+4kw==", "mode": 420, "size": 5887}, "test/uri-js.test.js": {"checkedAt": 1754554781182, "integrity": "sha512-sfF3Lr7JHT+9wNTsTLNxnhPDq2MZopHc0Rudhgd9+NWVIR+bOg298JmgiRQGbczmFJTf8u/+rFBJ8j6f0qMUWw==", "mode": 420, "size": 41117}, "test/util.test.js": {"checkedAt": 1754554781200, "integrity": "sha512-HfqB9sIK425zTFkBEKfxVah+YXfv/UuQiZ7Mc6maOc1xaaezKePeR9hQACIU6g0f2gFJmmh2nHVpMQHsoIgZTA==", "mode": 420, "size": 572}, "lib/utils.js": {"checkedAt": 1754554781215, "integrity": "sha512-i1TMWFzSazYBE63Xi9/0mHar3bLzFDAsbrfRSJQPMsmQ7ZsgHIPbS7MYD5N/1FhFqxQ0iELwMpba6IS637Qxog==", "mode": 420, "size": 5823}, "package.json": {"checkedAt": 1754554781230, "integrity": "sha512-brnp48EuQc09A/loN7UpnsGsIMvgioUWOaIcTcjrA62iqHhpmPF7PKJ9s4u60whp0NBDid7X8dM1zCTXznQl6A==", "mode": 420, "size": 1717}, "README.md": {"checkedAt": 1754554781246, "integrity": "sha512-tFa+ULgVb/4GhC1c3KHHewjgPcBU8udQ1+C53MMtP5dL/LS2BKPzdJGKaR++2nhsw4QuC2l38951V2evd9j/wg==", "mode": 420, "size": 4486}, "types/index.d.ts": {"checkedAt": 1754554781261, "integrity": "sha512-NpYv7+5J9ym5rrpkF6Y1uhRHB+XNeRjMHarSrSh9cLSFwkXvC0XlNbJ08ugZ1ccfWlgWpd0SoXrrZUnVeua0fg==", "mode": 420, "size": 1460}, "types/index.test-d.ts": {"checkedAt": 1754554781277, "integrity": "sha512-svlnCpTJDXNT/vtXlxK30qT6k7ySuqb8MygFn39I77OMWM+KSJ+FEItsAs/FnSlfzYyADEhp97msXxRIO3Y5Ww==", "mode": 420, "size": 479}, ".github/.stale.yml": {"checkedAt": 1754554781302, "integrity": "sha512-bENWs0+KY5YXoptvV0UMfWUK9SGF4Hkud4Z1OfGXotgMM0GQsWUpZvB56whAF7K3t9zi5KO72bwAoI8t1lSE9g==", "mode": 420, "size": 771}, ".github/workflows/ci.yml": {"checkedAt": 1754554781322, "integrity": "sha512-9KwnAiA8iHwa19tSOJE5P12Zii2TkO3se9ydqpT26LDfbOdjWGuV36v5DG3Kos6eMPZ3YxF/yMm0z7jgKfpBpg==", "mode": 420, "size": 1312}, ".github/dependabot.yml": {"checkedAt": 1754554781340, "integrity": "sha512-2itnF+l0ZJRhyduI9wpz7Di/wajJCZqUT3l7tlAxVvJ+GHDbMAJyJCkFe2qHG5vHSa4lgRgzMYD4QH/3wvyIAw==", "mode": 420, "size": 274}, ".github/workflows/package-manager-ci.yml": {"checkedAt": 1754554781359, "integrity": "sha512-ArkGCP1TGc8x5+mhRNxTMtLYPVxpcCatsOqXKvJUvBXrLWSlkS5Qyx0MqaLrUz7kHENbXDSbJ9n6Ot0phMU1TA==", "mode": 420, "size": 315}, ".github/tests_checker.yml": {"checkedAt": 1754554781374, "integrity": "sha512-Nj1M0nGKcW05zpHLeTacEZiIIftmADd/Lw+Ip95sh3kZFI9Xjk6qQpW6//gbD/3qhnxePByUe8qHsWQ7utq7qQ==", "mode": 420, "size": 223}}}